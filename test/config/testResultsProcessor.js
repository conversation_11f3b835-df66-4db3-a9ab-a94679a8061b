/**
 * 测试结果处理器
 * 处理和格式化Jest测试结果，符合SOP要求
 */

const fs = require('fs');
const path = require('path');

function processTestResults(testResults) {
  const processedResults = {
    ...testResults,
    processedAt: new Date().toISOString(),
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      env: process.env.NODE_ENV
    },
    summary: generateSummary(testResults),
    recommendations: generateRecommendations(testResults)
  };

  // 保存处理后的结果
  saveProcessedResults(processedResults);
  
  // 生成测试报告
  generateTestReport(processedResults);
  
  return processedResults;
}

function generateSummary(testResults) {
  const summary = {
    total: testResults.numTotalTests,
    passed: testResults.numPassedTests,
    failed: testResults.numFailedTests,
    skipped: testResults.numPendingTests,
    duration: testResults.testResults.reduce((total, result) => {
      return total + (result.perfStats?.end - result.perfStats?.start || 0);
    }, 0),
    successRate: testResults.numTotalTests > 0 
      ? (testResults.numPassedTests / testResults.numTotalTests * 100).toFixed(2)
      : 0
  };

  // 按测试类型分组
  summary.byType = {};
  testResults.testResults.forEach(result => {
    const displayName = result.displayName?.name || 'Unknown';
    if (!summary.byType[displayName]) {
      summary.byType[displayName] = {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0
      };
    }
    
    const typeStats = summary.byType[displayName];
    result.testResults.forEach(test => {
      typeStats.total++;
      typeStats.duration += test.duration || 0;
      
      switch (test.status) {
        case 'passed':
          typeStats.passed++;
          break;
        case 'failed':
          typeStats.failed++;
          break;
        default:
          typeStats.skipped++;
      }
    });
  });

  return summary;
}

function generateRecommendations(testResults) {
  const recommendations = [];
  const summary = generateSummary(testResults);

  // 成功率建议
  if (summary.successRate < 90) {
    recommendations.push({
      type: 'quality',
      priority: 'high',
      message: `测试成功率 ${summary.successRate}% 低于推荐的90%，需要修复失败的测试`
    });
  }

  // 性能建议
  const avgDuration = summary.duration / summary.total;
  if (avgDuration > 1000) {
    recommendations.push({
      type: 'performance',
      priority: 'medium',
      message: `平均测试时间 ${avgDuration.toFixed(2)}ms 较长，考虑优化测试性能`
    });
  }

  // 覆盖率建议
  if (testResults.coverageMap) {
    const coverageSummary = testResults.coverageMap.getCoverageSummary();
    if (coverageSummary.statements.pct < 75) {
      recommendations.push({
        type: 'coverage',
        priority: 'medium',
        message: `代码覆盖率 ${coverageSummary.statements.pct}% 低于推荐的75%`
      });
    }
  }

  // 测试分布建议
  Object.entries(summary.byType).forEach(([type, stats]) => {
    if (type === 'UNIT' && stats.total < summary.total * 0.7) {
      recommendations.push({
        type: 'structure',
        priority: 'low',
        message: '单元测试数量偏少，建议增加单元测试以符合测试金字塔原则'
      });
    }
  });

  return recommendations;
}

function saveProcessedResults(results) {
  try {
    const outputDir = path.join(__dirname, '../reports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const outputFile = path.join(outputDir, 'processed-results.json');
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    
    console.log(`📊 处理后的测试结果已保存: ${outputFile}`);
  } catch (error) {
    console.error('❌ 保存处理结果失败:', error.message);
  }
}

function generateTestReport(results) {
  try {
    const reportDir = path.join(__dirname, '../reports');
    const reportFile = path.join(reportDir, 'test-report.md');
    
    const report = `# Firespoon API 测试报告

## 📊 测试概览

- **测试时间**: ${results.processedAt}
- **环境**: ${results.environment.env}
- **Node版本**: ${results.environment.nodeVersion}
- **平台**: ${results.environment.platform} ${results.environment.arch}

## 📈 测试结果

| 指标 | 数值 |
|------|------|
| 总测试数 | ${results.summary.total} |
| 通过 | ${results.summary.passed} |
| 失败 | ${results.summary.failed} |
| 跳过 | ${results.summary.skipped} |
| 成功率 | ${results.summary.successRate}% |
| 总耗时 | ${(results.summary.duration / 1000).toFixed(2)}s |

## 🔍 分类测试结果

${Object.entries(results.summary.byType).map(([type, stats]) => `
### ${type} 测试
- 总数: ${stats.total}
- 通过: ${stats.passed}
- 失败: ${stats.failed}
- 跳过: ${stats.skipped}
- 成功率: ${stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(2) : 0}%
- 耗时: ${(stats.duration / 1000).toFixed(2)}s
`).join('')}

## 💡 改进建议

${results.recommendations.length > 0 
  ? results.recommendations.map(rec => `
### ${rec.type.toUpperCase()} - ${rec.priority.toUpperCase()}
${rec.message}
`).join('')
  : '✅ 暂无改进建议，测试质量良好！'
}

## 📋 详细信息

完整的测试结果数据请查看: \`test/reports/processed-results.json\`

---
*报告生成时间: ${new Date().toISOString()}*
`;

    fs.writeFileSync(reportFile, report);
    console.log(`📄 测试报告已生成: ${reportFile}`);
  } catch (error) {
    console.error('❌ 生成测试报告失败:', error.message);
  }
}

module.exports = processTestResults;
