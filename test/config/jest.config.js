/**
 * Jest configuration for Firespoon API testing
 * 统一测试框架配置 - 支持多项目模式
 */
const path = require('path');

module.exports = {
  // 基础配置
  testEnvironment: 'node',
  rootDir: path.resolve(__dirname, '../..'),

  // 文件转换配置
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  moduleFileExtensions: ['js', 'json', 'node'],
  transformIgnorePatterns: ['node_modules/'],

  // 多项目配置 - 支持不同类型的测试
  projects: [
    // 单元测试项目
    {
      displayName: {
        name: 'UNIT',
        color: 'blue'
      },
      testEnvironment: 'node',
      rootDir: path.resolve(__dirname, '../..'),
      testMatch: ['<rootDir>/test/unit/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/test/config/unit.setup.js',
        '<rootDir>/test/config/enhanced-expect.js'
      ],
      transform: {
        '^.+\\.js$': 'babel-jest'
      },
      moduleFileExtensions: ['js', 'json', 'node'],
      transformIgnorePatterns: ['node_modules/'],

      // 单元测试特定配置
      maxWorkers: '50%',
      resetModules: true,
      clearMocks: true,
      restoreMocks: true,

      // 覆盖率配置
      collectCoverageFrom: [
        'graphql/**/*.js',
        'models/**/*.js',
        'routes/**/*.js',
        'services/**/*.js',
        'whatsapp/**/*.js',
        'controllers/**/*.js',
        'helpers/**/*.js',
        'middleware/**/*.js',
        '!**/node_modules/**',
        '!**/test/**',
        '!**/coverage/**'
      ],
      coverageThreshold: {
        global: {
          statements: 75,
          branches: 65,
          functions: 75,
          lines: 75
        }
      },
      coverageDirectory: '<rootDir>/test/coverage/unit'
    },

    // 集成测试项目
    {
      displayName: {
        name: 'INTEGRATION',
        color: 'green'
      },
      testEnvironment: 'node',
      rootDir: path.resolve(__dirname, '../..'),
      testMatch: ['<rootDir>/test/integration/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/test/config/integration.setup.js',
        '<rootDir>/test/config/enhanced-expect.js'
      ],
      globalSetup: '<rootDir>/test/config/globalSetup.js',
      globalTeardown: '<rootDir>/test/config/globalTeardown.js',
      transform: {
        '^.+\\.js$': 'babel-jest'
      },
      moduleFileExtensions: ['js', 'json', 'node'],
      transformIgnorePatterns: ['node_modules/'],

      // 集成测试特定配置
      maxWorkers: 1, // 串行运行避免数据库冲突
      resetModules: false // 保持模块状态
    },

    // E2E测试项目
    {
      displayName: {
        name: 'E2E',
        color: 'magenta'
      },
      testEnvironment: 'node',
      rootDir: path.resolve(__dirname, '../..'),
      testMatch: ['<rootDir>/test/e2e/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/test/config/e2e.setup.js',
        '<rootDir>/test/config/enhanced-expect.js'
      ],
      globalSetup: '<rootDir>/test/config/globalSetup.js',
      globalTeardown: '<rootDir>/test/config/globalTeardown.js',
      transform: {
        '^.+\\.js$': 'babel-jest'
      },
      moduleFileExtensions: ['js', 'json', 'node'],
      transformIgnorePatterns: ['node_modules/'],

      // E2E测试特定配置
      maxWorkers: 1, // 串行运行
      resetModules: false
    },

    // 性能测试项目
    {
      displayName: {
        name: 'PERFORMANCE',
        color: 'yellow'
      },
      testEnvironment: 'node',
      rootDir: path.resolve(__dirname, '../..'),
      testMatch: ['<rootDir>/test/performance/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/test/config/performance.setup.js',
        '<rootDir>/test/config/enhanced-expect.js'
      ],
      globalSetup: '<rootDir>/test/config/globalSetup.js',
      globalTeardown: '<rootDir>/test/config/globalTeardown.js',
      transform: {
        '^.+\\.js$': 'babel-jest'
      },
      moduleFileExtensions: ['js', 'json', 'node'],
      transformIgnorePatterns: ['node_modules/'],

      // 性能测试特定配置
      maxWorkers: 1, // 串行运行
      resetModules: false
    }
  ],

  // 全局报告配置
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/test/reports',
        outputName: 'test-results.xml',
        suiteName: 'Firespoon API Tests',
        classNameTemplate: '{displayName}.{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true
      }
    ],
    [
      '<rootDir>/test/config/customReporter.js',
      {
        outputFile: '<rootDir>/test/reports/custom-report.json'
      }
    ],
    [
      '<rootDir>/test/config/detailedLogReporter.js',
      {}
    ]
  ],

  // 监视插件
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],

  // 全局配置
  testTimeout: 30000, // 默认30秒超时
  forceExit: true,
  detectLeaks: false,
  verbose: false,
  collectCoverage: false, // 通过项目级别控制
  coverageDirectory: '<rootDir>/test/coverage',
  coverageReporters: ['text', 'text-summary', 'lcov', 'clover', 'html', 'json'],

  // 测试结果处理
  testResultsProcessor: '<rootDir>/test/config/testResultsProcessor.js'
};
