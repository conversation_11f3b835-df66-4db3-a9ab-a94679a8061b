<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Firespoon API Tests" tests="2" failures="0" errors="0" time="0.794">
  <testsuite name="Refund System Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T13:59:59" time="0.71" tests="2">
    <testcase classname="INTEGRATION.Refund System Integration Tests › refundStatus Default Value Tests" name="should verify refundStatus default value is NONE" time="0.097">
    </testcase>
    <testcase classname="INTEGRATION.Refund System Integration Tests › refundStatus Default Value Tests" name="should verify refund status enum values" time="0.043">
    </testcase>
  </testsuite>
</testsuites>