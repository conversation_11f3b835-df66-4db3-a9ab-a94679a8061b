[2025-06-23T13:59:59.237Z] ================================================================================
[2025-06-23T13:59:59.237Z] Test Run Started
[2025-06-23T13:59:59.237Z] Timestamp: 2025-06-23T13:59:59.236Z
[2025-06-23T13:59:59.237Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-23T13:59:59.238Z] Runtime Version: Node.js v18.20.5
[2025-06-23T13:59:59.238Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-23T13:59:59.238Z] ================================================================================
[2025-06-23T13:59:59.281Z] ================================================================================
[2025-06-23T13:59:59.282Z] Test Run Started
[2025-06-23T13:59:59.282Z] Timestamp: 2025-06-23T13:59:59.282Z
[2025-06-23T13:59:59.282Z] Operating System: linux x64
[2025-06-23T13:59:59.282Z] Runtime Version: Node.js v18.20.5
[2025-06-23T13:59:59.282Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-23T13:59:59.282Z] ================================================================================
[2025-06-23T13:59:59.282Z] [RUN START] - Test execution beginning
[2025-06-23T13:59:59.282Z] Total Test Suites: 1
[2025-06-23T13:59:59.282Z] Test Environment: test
[2025-06-23T13:59:59.292Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-23T13:59:59.292Z] Suite Display Name: INTEGRATION
[2025-06-23T13:59:59.870Z]   [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-23T13:59:59.870Z]   Module: refund
[2025-06-23T13:59:59.870Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-23T13:59:59.967Z]   [Arrange] - Precondition: Test environment prepared for "should verify refundStatus default value is NONE"
[2025-06-23T13:59:59.967Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:59:59.967Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:59:59.967Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:59:59.967Z]   [Act] - Step: Executing test logic for "should verify refundStatus default value is NONE"
[2025-06-23T13:59:59.967Z]   [Act Log] - Loading target module: /test/integration/refund.integration.js
[2025-06-23T13:59:59.967Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:59:59.967Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:59:59.967Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:59:59.968Z]   [Act Log] - Captured 34 program outputs during execution
[2025-06-23T13:59:59.968Z]   [Act Log] - process: 34 outputs
[2025-06-23T13:59:59.968Z]   [Act Log] - === Program Output Details ===
[2025-06-23T13:59:59.968Z]   [Act Log] - +32ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +36ms [process.stdout]   console.log
    Test database cleared

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +41ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +42ms [process.stdout]   console.log
    📝 Testing refundStatus default value...

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +84ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +85ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +85ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +86ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +86ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +86ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +87ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +87ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +88ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +88ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +88ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +89ms [process.stdout]   console.log
          Expected: 0

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +89ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +90ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +90ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +90ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +91ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +92ms [process.stdout]   console.log
        ✓ Assert: toEqual

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +92ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +93ms [process.stdout]   console.log
          Expected: []

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +93ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +93ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +93ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +94ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +94ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +94ms [process.stdout]   console.log
    ✅ refundStatus default value test passed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +94ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +95ms [process.stdout]   console.log
    Order refundStatus: NONE

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - +95ms [process.stdout] 
[2025-06-23T13:59:59.968Z]   [Act Log] - +96ms [process.stdout]   console.log
    📝 Test "Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE" captured 45 logs

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T13:59:59.968Z]   [Act Log] - === End Program Output ===
[2025-06-23T13:59:59.968Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:59:59.968Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:59:59.968Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:59:59.968Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:59:59.968Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:59:59.968Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:59:59.969Z]   [CASE END] - Duration: 97ms
[2025-06-23T13:59:59.969Z]   [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-23T13:59:59.969Z]   Module: refund
[2025-06-23T13:59:59.969Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-23T14:00:00.012Z]   [Arrange] - Precondition: Test environment prepared for "should verify refund status enum values"
[2025-06-23T14:00:00.012Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T14:00:00.012Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T14:00:00.012Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T14:00:00.012Z]   [Act] - Step: Executing test logic for "should verify refund status enum values"
[2025-06-23T14:00:00.012Z]   [Act Log] - Loading target module: /test/integration/refund.integration.js
[2025-06-23T14:00:00.012Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T14:00:00.012Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T14:00:00.012Z]   [Act Log] - Invoking target method or function
[2025-06-23T14:00:00.012Z]   [Act Log] - Captured 40 program outputs during execution
[2025-06-23T14:00:00.012Z]   [Act Log] - process: 40 outputs
[2025-06-23T14:00:00.012Z]   [Act Log] - === Program Output Details ===
[2025-06-23T14:00:00.012Z]   [Act Log] - +10ms [process.stdout] 
[2025-06-23T14:00:00.012Z]   [Act Log] - +11ms [process.stdout]   console.log
    Test database cleared

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.012Z]   [Act Log] - +13ms [process.stdout] 
[2025-06-23T14:00:00.012Z]   [Act Log] - +14ms [process.stdout]   console.log
    📝 Testing refund status enum values...

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.012Z]   [Act Log] - +20ms [process.stdout] 
[2025-06-23T14:00:00.012Z]   [Act Log] - +21ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.012Z]   [Act Log] - +21ms [process.stdout] 
[2025-06-23T14:00:00.012Z]   [Act Log] - +21ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.012Z]   [Act Log] - +21ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +22ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +22ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +22ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +26ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +27ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +27ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +27ms [process.stdout]   console.log
          Expected: "PARTIAL"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +27ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +28ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +28ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +29ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +33ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +34ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +34ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +34ms [process.stdout]   console.log
          Expected: "FULL"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +34ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +35ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +35ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +36ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +39ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +40ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +40ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +40ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +40ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +41ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +41ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +41ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +41ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +42ms [process.stdout]   console.log
    ✅ Refund status enum test passed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - +42ms [process.stdout] 
[2025-06-23T14:00:00.013Z]   [Act Log] - +43ms [process.stdout]   console.log
    📝 Test "Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values" captured 54 logs

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.013Z]   [Act Log] - === End Program Output ===
[2025-06-23T14:00:00.013Z]   [Act Log] - Method execution completed successfully
[2025-06-23T14:00:00.013Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T14:00:00.013Z]   [Act Log] - All function calls returned expected types
[2025-06-23T14:00:00.013Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T14:00:00.013Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T14:00:00.013Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T14:00:00.013Z]   [CASE END] - Duration: 43ms
[2025-06-23T14:00:00.026Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-23T14:00:00.027Z] 
[CASE START] - I-AUTO-001: should verify refundStatus default value is NONE
[2025-06-23T14:00:00.027Z] Module: refund
[2025-06-23T14:00:00.027Z] Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-23T14:00:00.027Z] [Arrange] - Precondition: Setting up test environment
[2025-06-23T14:00:00.027Z] [Act] - Step: Executing test logic
[2025-06-23T14:00:00.027Z] [Act Log] - Captured 105 program outputs during execution
[2025-06-23T14:00:00.027Z] [Act Log] - === Program Output Details ===
[2025-06-23T14:00:00.027Z] [Act Log] - +34ms [console.log] Console log level: debug
[2025-06-23T14:00:00.027Z] [Act Log] - +34ms [process.stdout] Console log level: debug

[2025-06-23T14:00:00.027Z] [Act Log] - +37ms [console.log] File log level: debug
[2025-06-23T14:00:00.027Z] [Act Log] - +37ms [process.stdout] File log level: debug

[2025-06-23T14:00:00.027Z] [Act Log] - +39ms [console.log] 📝 日志捕获器已启动
[2025-06-23T14:00:00.027Z] [Act Log] - +39ms [process.stdout] 📝 日志捕获器已启动

[2025-06-23T14:00:00.027Z] [Act Log] - +40ms [process.stderr] 
[2025-06-23T14:00:00.027Z] [Act Log] - +517ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +541ms [process.stdout]   console.log
    Console log level: debug

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.027Z] [Act Log] - +549ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +550ms [process.stdout]   console.log
    File log level: debug

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.027Z] [Act Log] - +551ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +555ms [process.stdout]   console.log
    ✓ Enhanced expect initialized - 断言值显示已启用

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.027Z] [Act Log] - +625ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +627ms [process.stdout]   console.log
    🔧 Simple refund test environment ready

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.027Z] [Act Log] - +660ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +664ms [process.stdout]   console.log
    Test database cleared

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.027Z] [Act Log] - +669ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +670ms [process.stdout]   console.log
    📝 Testing refundStatus default value...

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.027Z] [Act Log] - +712ms [process.stdout] 
[2025-06-23T14:00:00.027Z] [Act Log] - +713ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +713ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +714ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +714ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +714ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +715ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +715ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +716ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +716ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +716ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +717ms [process.stdout]   console.log
          Expected: 0

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +717ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +718ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +718ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +718ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +719ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +720ms [process.stdout]   console.log
        ✓ Assert: toEqual

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +720ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +721ms [process.stdout]   console.log
          Expected: []

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +721ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +721ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +721ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +722ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +722ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +722ms [process.stdout]   console.log
    ✅ refundStatus default value test passed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +722ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +723ms [process.stdout]   console.log
    Order refundStatus: NONE

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +723ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +724ms [process.stdout]   console.log
    📝 Test "Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE" captured 45 logs

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +737ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +738ms [process.stdout]   console.log
    Test database cleared

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +740ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +741ms [process.stdout]   console.log
    📝 Testing refund status enum values...

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +747ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +748ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +748ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +748ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +748ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +749ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +749ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +749ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +753ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +754ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +754ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +754ms [process.stdout]   console.log
          Expected: "PARTIAL"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +754ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +755ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +755ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +756ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +760ms [process.stdout] 
[2025-06-23T14:00:00.028Z] [Act Log] - +761ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.028Z] [Act Log] - +761ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +761ms [process.stdout]   console.log
          Expected: "FULL"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +761ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +762ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +762ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +763ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +766ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +767ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +767ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +767ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +767ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +768ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +768ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +768ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +768ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +769ms [process.stdout]   console.log
    ✅ Refund status enum test passed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +769ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +770ms [process.stdout]   console.log
    📝 Test "Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values" captured 54 logs

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +779ms [process.stdout] 
[2025-06-23T14:00:00.029Z] [Act Log] - +780ms [process.stdout]   console.log
    🧹 Simple refund test cleanup completed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.029Z] [Act Log] - +783ms [process.stderr]  PASS   INTEGRATION  test/integration/refund.integration.test.js

[2025-06-23T14:00:00.029Z] [Act Log] - +783ms [process.stderr]   Refund System Integration Tests

[2025-06-23T14:00:00.029Z] [Act Log] - +783ms [process.stderr]     refundStatus Default Value Tests

[2025-06-23T14:00:00.029Z] [Act Log] - +783ms [process.stderr]       ✓ should verify refundStatus default value is NONE (97 ms)

[2025-06-23T14:00:00.029Z] [Act Log] - +783ms [process.stderr]       ✓ should verify refund status enum values (43 ms)

[2025-06-23T14:00:00.029Z] [Act Log] - +783ms [process.stderr] 

[2025-06-23T14:00:00.029Z] [Act Log] - +784ms [console.log] [PASS] - I-AUTO-001: should verify refundStatus default value is NONE
[2025-06-23T14:00:00.029Z] [Act Log] - +784ms [process.stdout] [PASS] - I-AUTO-001: should verify refundStatus default value is NONE

[2025-06-23T14:00:00.029Z] [Act Log] - +784ms [console.log] ├─ Expected: Test should complete successfully
[2025-06-23T14:00:00.029Z] [Act Log] - +784ms [process.stdout] ├─ Expected: Test should complete successfully

[2025-06-23T14:00:00.029Z] [Act Log] - +784ms [console.log] └─ Actual: Test completed without errors
[2025-06-23T14:00:00.029Z] [Act Log] - +784ms [process.stdout] └─ Actual: Test completed without errors

[2025-06-23T14:00:00.029Z] [Act Log] - +785ms [console.log] 📊 Found 103 captured logs from global capture
[2025-06-23T14:00:00.029Z] [Act Log] - +785ms [process.stdout] 📊 Found 103 captured logs from global capture

[2025-06-23T14:00:00.029Z] [Act Log] - === End Program Output ===
[2025-06-23T14:00:00.029Z] [Assert] - Verifying: All assertions passed
[2025-06-23T14:00:00.029Z] [Assert Log] - Expected: Test completion without errors
[2025-06-23T14:00:00.029Z] [Assert Log] - Actual: Test completed successfully
[2025-06-23T14:00:00.029Z] [RESULT] - PASS: Test completed successfully
[2025-06-23T14:00:00.030Z] [CASE END] - Duration: 97ms
[2025-06-23T14:00:00.030Z] 
[CASE START] - I-AUTO-002: should verify refund status enum values
[2025-06-23T14:00:00.030Z] Module: refund
[2025-06-23T14:00:00.030Z] Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-23T14:00:00.030Z] [Arrange] - Precondition: Setting up test environment
[2025-06-23T14:00:00.030Z] [Act] - Step: Executing test logic
[2025-06-23T14:00:00.030Z] [Act Log] - Captured 113 program outputs during execution
[2025-06-23T14:00:00.030Z] [Act Log] - === Program Output Details ===
[2025-06-23T14:00:00.030Z] [Act Log] - +34ms [console.log] Console log level: debug
[2025-06-23T14:00:00.030Z] [Act Log] - +34ms [process.stdout] Console log level: debug

[2025-06-23T14:00:00.030Z] [Act Log] - +37ms [console.log] File log level: debug
[2025-06-23T14:00:00.030Z] [Act Log] - +37ms [process.stdout] File log level: debug

[2025-06-23T14:00:00.030Z] [Act Log] - +39ms [console.log] 📝 日志捕获器已启动
[2025-06-23T14:00:00.030Z] [Act Log] - +39ms [process.stdout] 📝 日志捕获器已启动

[2025-06-23T14:00:00.030Z] [Act Log] - +40ms [process.stderr] 
[2025-06-23T14:00:00.030Z] [Act Log] - +517ms [process.stdout] 
[2025-06-23T14:00:00.030Z] [Act Log] - +541ms [process.stdout]   console.log
    Console log level: debug

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.030Z] [Act Log] - +549ms [process.stdout] 
[2025-06-23T14:00:00.030Z] [Act Log] - +550ms [process.stdout]   console.log
    File log level: debug

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.030Z] [Act Log] - +551ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +555ms [process.stdout]   console.log
    ✓ Enhanced expect initialized - 断言值显示已启用

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +625ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +627ms [process.stdout]   console.log
    🔧 Simple refund test environment ready

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +660ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +664ms [process.stdout]   console.log
    Test database cleared

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +669ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +670ms [process.stdout]   console.log
    📝 Testing refundStatus default value...

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +712ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +713ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +713ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +714ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +714ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +714ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +715ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +715ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +716ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +716ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +716ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +717ms [process.stdout]   console.log
          Expected: 0

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +717ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +718ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +718ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +718ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +719ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +720ms [process.stdout]   console.log
        ✓ Assert: toEqual

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +720ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +721ms [process.stdout]   console.log
          Expected: []

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +721ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +721ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +721ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +722ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +722ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +722ms [process.stdout]   console.log
    ✅ refundStatus default value test passed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +722ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +723ms [process.stdout]   console.log
    Order refundStatus: NONE

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +723ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +724ms [process.stdout]   console.log
    📝 Test "Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE" captured 45 logs

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +737ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +738ms [process.stdout]   console.log
    Test database cleared

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +740ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +741ms [process.stdout]   console.log
    📝 Testing refund status enum values...

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +747ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +748ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +748ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +748ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +748ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +749ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.031Z] [Act Log] - +749ms [process.stdout] 
[2025-06-23T14:00:00.031Z] [Act Log] - +749ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +753ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +754ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +754ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +754ms [process.stdout]   console.log
          Expected: "PARTIAL"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +754ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +755ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +755ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +756ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +760ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +761ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +761ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +761ms [process.stdout]   console.log
          Expected: "FULL"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +761ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +762ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +762ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +763ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +766ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +767ms [process.stdout]   console.log
        ✓ Assert: toBe

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +767ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +767ms [process.stdout]   console.log
          Expected: "NONE"

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +767ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +768ms [process.stdout]   console.log
          Actual: undefined

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +768ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +768ms [process.stdout]   console.log
          Result: PASS

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +768ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +769ms [process.stdout]   console.log
    ✅ Refund status enum test passed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +769ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +770ms [process.stdout]   console.log
    📝 Test "Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values" captured 54 logs

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +779ms [process.stdout] 
[2025-06-23T14:00:00.032Z] [Act Log] - +780ms [process.stdout]   console.log
    🧹 Simple refund test cleanup completed

      at ActLogCapture.apply [as captureLog] (test/config/actLogCapture.js:106:47)


[2025-06-23T14:00:00.032Z] [Act Log] - +783ms [process.stderr]  PASS   INTEGRATION  test/integration/refund.integration.test.js

[2025-06-23T14:00:00.032Z] [Act Log] - +783ms [process.stderr]   Refund System Integration Tests

[2025-06-23T14:00:00.032Z] [Act Log] - +783ms [process.stderr]     refundStatus Default Value Tests

[2025-06-23T14:00:00.032Z] [Act Log] - +783ms [process.stderr]       ✓ should verify refundStatus default value is NONE (97 ms)

[2025-06-23T14:00:00.032Z] [Act Log] - +783ms [process.stderr]       ✓ should verify refund status enum values (43 ms)

[2025-06-23T14:00:00.032Z] [Act Log] - +783ms [process.stderr] 

[2025-06-23T14:00:00.032Z] [Act Log] - +784ms [console.log] [PASS] - I-AUTO-001: should verify refundStatus default value is NONE
[2025-06-23T14:00:00.032Z] [Act Log] - +784ms [process.stdout] [PASS] - I-AUTO-001: should verify refundStatus default value is NONE

[2025-06-23T14:00:00.032Z] [Act Log] - +784ms [console.log] ├─ Expected: Test should complete successfully
[2025-06-23T14:00:00.032Z] [Act Log] - +784ms [process.stdout] ├─ Expected: Test should complete successfully

[2025-06-23T14:00:00.032Z] [Act Log] - +784ms [console.log] └─ Actual: Test completed without errors
[2025-06-23T14:00:00.032Z] [Act Log] - +784ms [process.stdout] └─ Actual: Test completed without errors

[2025-06-23T14:00:00.032Z] [Act Log] - +785ms [console.log] 📊 Found 103 captured logs from global capture
[2025-06-23T14:00:00.032Z] [Act Log] - +785ms [process.stdout] 📊 Found 103 captured logs from global capture

[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [console.log] [PASS] - I-AUTO-002: should verify refund status enum values
[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [process.stdout] [PASS] - I-AUTO-002: should verify refund status enum values

[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [console.log] ├─ Expected: Test should complete successfully
[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [process.stdout] ├─ Expected: Test should complete successfully

[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [console.log] └─ Actual: Test completed without errors
[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [process.stdout] └─ Actual: Test completed without errors

[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [console.log] 📊 Found 111 captured logs from global capture
[2025-06-23T14:00:00.032Z] [Act Log] - +788ms [process.stdout] 📊 Found 111 captured logs from global capture

[2025-06-23T14:00:00.032Z] [Act Log] - === End Program Output ===
[2025-06-23T14:00:00.032Z] [Assert] - Verifying: All assertions passed
[2025-06-23T14:00:00.032Z] [Assert Log] - Expected: Test completion without errors
[2025-06-23T14:00:00.032Z] [Assert Log] - Actual: Test completed successfully
[2025-06-23T14:00:00.033Z] [RESULT] - PASS: Test completed successfully
[2025-06-23T14:00:00.033Z] [CASE END] - Duration: 43ms
[2025-06-23T14:00:00.033Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-23T14:00:00.033Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-23T14:00:00.033Z] Suite Duration: 710ms
[2025-06-23T14:00:00.033Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T14:00:00.048Z] 
================================================================================
[2025-06-23T14:00:00.049Z] Test Run Finished
[2025-06-23T14:00:00.049Z] End Timestamp: 2025-06-23T14:00:00.049Z
[2025-06-23T14:00:00.049Z] Total Duration: 806ms (0.81s)
[2025-06-23T14:00:00.049Z] Total Tests: 2
[2025-06-23T14:00:00.049Z] Passed Tests: 2
[2025-06-23T14:00:00.049Z] Failed Tests: 0
[2025-06-23T14:00:00.049Z] Skipped Tests: 0
[2025-06-23T14:00:00.049Z] Success Rate: 100.00%
[2025-06-23T14:00:00.049Z] Overall Result: FAILURE
[2025-06-23T14:00:00.049Z] ================================================================================
[2025-06-23T14:00:00.050Z] 
================================================================================
[2025-06-23T14:00:00.050Z] Test Run Finished
[2025-06-23T14:00:00.050Z] End Timestamp: 2025-06-23T14:00:00.050Z
[2025-06-23T14:00:00.050Z] Total Duration: 814ms (0.81s)
[2025-06-23T14:00:00.050Z] 
[STATISTICS]
[2025-06-23T14:00:00.050Z] Total Test Suites: 1
[2025-06-23T14:00:00.050Z] Passed Test Suites: 1
[2025-06-23T14:00:00.050Z] Failed Test Suites: 0
[2025-06-23T14:00:00.050Z] Total Tests: 2
[2025-06-23T14:00:00.050Z] Passed Tests: 2
[2025-06-23T14:00:00.050Z] Failed Tests: 0
[2025-06-23T14:00:00.050Z] Skipped Tests: 0
[2025-06-23T14:00:00.050Z] Success Rate: 100.00%
[2025-06-23T14:00:00.050Z] Overall Result: FAILURE
[2025-06-23T14:00:00.050Z] ================================================================================
