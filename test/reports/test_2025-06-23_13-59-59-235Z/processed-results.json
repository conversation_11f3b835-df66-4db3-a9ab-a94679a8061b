{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 2, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 2, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750687199241, "success": true, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750687200023, "runtime": 710, "slow": false, "start": 1750687199313}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js", "testResults": [{"ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "duration": 97, "failureDetails": [], "failureMessages": [], "fullName": "Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should verify refundStatus default value is NONE"}, {"ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "duration": 43, "failureDetails": [], "failureMessages": [], "fullName": "Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should verify refund status enum values"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}], "wasInterrupted": false, "processedAt": "2025-06-23T14:00:00.449Z", "environment": {"nodeVersion": "v18.20.5", "platform": "linux", "arch": "x64", "env": "test"}, "summary": {"total": 2, "passed": 2, "failed": 0, "skipped": 0, "duration": 710, "successRate": "100.00", "byType": {"INTEGRATION": {"total": 2, "passed": 2, "failed": 0, "skipped": 0, "duration": 140}}}, "recommendations": []}