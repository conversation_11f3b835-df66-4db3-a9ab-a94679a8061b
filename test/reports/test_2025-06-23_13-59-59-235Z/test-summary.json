{"summary": {"totalTests": 2, "passedTests": 2, "failedTests": 0, "skippedTests": 0, "totalDuration": 806, "success": false, "timestamp": "2025-06-23T14:00:00.048Z", "environment": "test"}, "projects": {"INTEGRATION": {"tests": [{"caseId": "I-AUTO-001", "title": "should verify refundStatus default value is NONE", "status": "passed", "duration": 97, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "capturedLogs": 105}, {"caseId": "I-AUTO-002", "title": "should verify refund status enum values", "status": "passed", "duration": 43, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "capturedLogs": 113}], "summary": {"total": 2, "passed": 2, "failed": 0, "skipped": 0, "duration": 140}}}, "coverage": {}, "performance": {}}