{"summary": {"totalTests": 52, "passedTests": 45, "failedTests": 7, "skippedTests": 0, "totalDuration": 21501, "success": false, "timestamp": "2025-06-23T13:28:03.483Z", "environment": "test"}, "projects": {"INTEGRATION": {"tests": [{"caseId": "I-AUTO-001", "title": "should verify refundStatus default value is NONE", "status": "passed", "duration": 285, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "capturedLogs": 18}, {"caseId": "I-AUTO-002", "title": "should verify refund status enum values", "status": "passed", "duration": 115, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "capturedLogs": 26}, {"caseId": "I-AUTO-001", "title": "should create a new order", "status": "failed", "duration": 8, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 38}, {"caseId": "I-AUTO-002", "title": "should fail to create order with invalid restaurant ID", "status": "failed", "duration": 2, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 46}, {"caseId": "I-AUTO-003", "title": "should fail to create order without authentication", "status": "failed", "duration": 1, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 54}, {"caseId": "I-AUTO-004", "title": "should update order status", "status": "failed", "duration": 1, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 62}, {"caseId": "I-AUTO-001", "title": "should process incoming message and create session", "status": "failed", "duration": 8, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["WhatsApp Webhook Integration"], "capturedLogs": 74}, {"caseId": "I-AUTO-002", "title": "should reject webhook with invalid signature", "status": "failed", "duration": 1, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["WhatsApp Webhook Integration"], "capturedLogs": 82}, {"caseId": "I-AUTO-003", "title": "should handle malformed webhook payload", "status": "failed", "duration": 3, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["WhatsApp Webhook Integration"], "capturedLogs": 90}, {"caseId": "I-AUTO-001", "title": "should create real payment intent with <PERSON>e", "status": "passed", "duration": 540, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 109}, {"caseId": "I-AUTO-002", "title": "should retrieve real payment intent status", "status": "passed", "duration": 89, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 117}, {"caseId": "I-AUTO-003", "title": "should handle payment with test card", "status": "passed", "duration": 64, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 125}, {"caseId": "I-AUTO-004", "title": "should handle declined card", "status": "passed", "duration": 62, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 133}, {"caseId": "I-AUTO-005", "title": "should create real Stripe customer", "status": "passed", "duration": 45, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"], "capturedLogs": 141}, {"caseId": "I-AUTO-006", "title": "should retrieve real Stripe customer", "status": "passed", "duration": 40, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"], "capturedLogs": 149}, {"caseId": "I-AUTO-007", "title": "should process real webhook signature validation", "status": "passed", "duration": 49, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Webhook Processing"], "capturedLogs": 157}, {"caseId": "I-AUTO-008", "title": "should handle invalid amount", "status": "passed", "duration": 68, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"], "capturedLogs": 165}, {"caseId": "I-AUTO-009", "title": "should handle invalid currency", "status": "passed", "duration": 47, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"], "capturedLogs": 173}, {"caseId": "I-AUTO-001", "title": "should have payment related types in schema", "status": "passed", "duration": 298, "failureMessages": [], "ancestorTitles": ["Payment System Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 255}, {"caseId": "I-AUTO-001", "title": "should have payment related types in schema", "status": "passed", "duration": 280, "failureMessages": [], "ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 307}, {"caseId": "I-AUTO-002", "title": "should have query types in schema", "status": "passed", "duration": 82, "failureMessages": [], "ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 315}, {"caseId": "I-AUTO-001", "title": "should have order query in schema", "status": "passed", "duration": 296, "failureMessages": [], "ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"], "capturedLogs": 325}, {"caseId": "I-AUTO-002", "title": "should have orders query in schema", "status": "passed", "duration": 74, "failureMessages": [], "ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"], "capturedLogs": 333}, {"caseId": "I-AUTO-001", "title": "should have order subscription in schema", "status": "passed", "duration": 289, "failureMessages": [], "ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"], "capturedLogs": 345}, {"caseId": "I-AUTO-002", "title": "should have mutation types in schema", "status": "passed", "duration": 83, "failureMessages": [], "ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"], "capturedLogs": 353}, {"caseId": "I-AUTO-001", "title": "should have payment related types in schema", "status": "passed", "duration": 293, "failureMessages": [], "ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 366}, {"caseId": "I-AUTO-002", "title": "should have mutation types in schema", "status": "passed", "duration": 88, "failureMessages": [], "ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 374}, {"caseId": "I-AUTO-001", "title": "should have Customer type in schema", "status": "passed", "duration": 314, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"], "capturedLogs": 390}, {"caseId": "I-AUTO-002", "title": "should have Address type in schema", "status": "passed", "duration": 70, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"], "capturedLogs": 398}, {"caseId": "I-AUTO-003", "title": "should have AddressInput type in schema", "status": "passed", "duration": 69, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Input Types"], "capturedLogs": 406}, {"caseId": "I-AUTO-004", "title": "should validate customer-related mutations exist", "status": "passed", "duration": 68, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"], "capturedLogs": 414}, {"caseId": "I-AUTO-005", "title": "should handle GraphQL validation errors", "status": "passed", "duration": 75, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"], "capturedLogs": 422}, {"caseId": "I-AUTO-001", "title": "should have updateOrderStatus mutation in schema", "status": "passed", "duration": 319, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "capturedLogs": 432}, {"caseId": "I-AUTO-002", "title": "should have Order type with orderStatus field in schema", "status": "passed", "duration": 75, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "capturedLogs": 440}, {"caseId": "I-AUTO-003", "title": "should have order status enum values in schema", "status": "passed", "duration": 61, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "capturedLogs": 448}, {"caseId": "I-AUTO-001", "title": "should respond to GraphQL endpoint", "status": "passed", "duration": 315, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"], "capturedLogs": 475}, {"caseId": "I-AUTO-002", "title": "should handle invalid GraphQL queries", "status": "passed", "duration": 114, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"], "capturedLogs": 483}, {"caseId": "I-AUTO-003", "title": "should have Restaurant type in schema", "status": "passed", "duration": 54, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Restaurant Schema"], "capturedLogs": 491}, {"caseId": "I-AUTO-004", "title": "should handle simple queries", "status": "passed", "duration": 52, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "capturedLogs": 499}, {"caseId": "I-AUTO-005", "title": "should validate GraphQL syntax", "status": "passed", "duration": 50, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "capturedLogs": 507}, {"caseId": "I-AUTO-006", "title": "should handle empty queries", "status": "passed", "duration": 59, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "capturedLogs": 515}, {"caseId": "I-AUTO-007", "title": "should support schema introspection", "status": "passed", "duration": 38, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "capturedLogs": 523}, {"caseId": "I-AUTO-008", "title": "should list available queries", "status": "passed", "duration": 47, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "capturedLogs": 531}, {"caseId": "I-AUTO-009", "title": "should list available mutations", "status": "passed", "duration": 47, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "capturedLogs": 539}, {"caseId": "I-AUTO-001", "title": "should respond to GraphQL introspection query", "status": "passed", "duration": 106, "failureMessages": [], "ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"], "capturedLogs": 608}, {"caseId": "I-AUTO-002", "title": "should have Restaurant type in schema", "status": "passed", "duration": 34, "failureMessages": [], "ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"], "capturedLogs": 616}, {"caseId": "I-AUTO-001", "title": "should have Order type in schema", "status": "passed", "duration": 106, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"], "capturedLogs": 667}, {"caseId": "I-AUTO-002", "title": "should have OrderStatus enum in schema", "status": "passed", "duration": 36, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"], "capturedLogs": 675}, {"caseId": "I-AUTO-003", "title": "should have OrderInput type in schema", "status": "passed", "duration": 35, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"], "capturedLogs": 683}, {"caseId": "I-AUTO-004", "title": "should validate GraphQL order operations", "status": "passed", "duration": 51, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"], "capturedLogs": 691}, {"caseId": "I-AUTO-005", "title": "should handle malformed order queries", "status": "passed", "duration": 45, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"], "capturedLogs": 699}, {"caseId": "I-AUTO-006", "title": "should validate required arguments", "status": "passed", "duration": 32, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"], "capturedLogs": 707}], "summary": {"total": 52, "passed": 45, "failed": 7, "skipped": 0, "duration": 5483}}}, "coverage": {}, "performance": {}}