window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":2,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":2,"startTime":1750687199241,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750687200023,"runtime":710,"slow":false,"start":1750687199313},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Refund System Integration Tests","refundStatus Default Value Tests"],"duration":97,"failureMessages":[],"fullName":"Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE","status":"passed","title":"should verify refundStatus default value is NONE"},{"ancestorTitles":["Refund System Integration Tests","refundStatus Default Value Tests"],"duration":43,"failureMessages":[],"fullName":"Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values","status":"passed","title":"should verify refund status enum values"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/home/<USER>/firespoon/Firespoon_API_TF/test/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","lcov","clover","html","json"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":true,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":15,"noStackTrace":false,"nonFlagArgs":["test/integration/refund.integration.test.js"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[{"displayName":{"name":"UNIT","color":"blue"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/unit/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/unit.setup.js","<rootDir>/test/config/enhanced-expect.js"],"transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":"50%","resetModules":true,"clearMocks":true,"restoreMocks":true,"collectCoverageFrom":["graphql/**/*.js","models/**/*.js","routes/**/*.js","services/**/*.js","whatsapp/**/*.js","controllers/**/*.js","helpers/**/*.js","middleware/**/*.js","!**/node_modules/**","!**/test/**","!**/coverage/**"],"coverageThreshold":{"global":{"statements":75,"branches":65,"functions":75,"lines":75}},"coverageDirectory":"<rootDir>/test/coverage/unit"},{"displayName":{"name":"INTEGRATION","color":"green"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/integration/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/integration.setup.js","<rootDir>/test/config/enhanced-expect.js"],"globalSetup":"<rootDir>/test/config/globalSetup.js","globalTeardown":"<rootDir>/test/config/globalTeardown.js","transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":1,"resetModules":false},{"displayName":{"name":"E2E","color":"magenta"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/e2e/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/e2e.setup.js","<rootDir>/test/config/enhanced-expect.js"],"globalSetup":"<rootDir>/test/config/globalSetup.js","globalTeardown":"<rootDir>/test/config/globalTeardown.js","transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":1,"resetModules":false},{"displayName":{"name":"PERFORMANCE","color":"yellow"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/performance/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/performance.setup.js","<rootDir>/test/config/enhanced-expect.js"],"globalSetup":"<rootDir>/test/config/globalSetup.js","globalTeardown":"<rootDir>/test/config/globalTeardown.js","transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":1,"resetModules":false}],"reporters":[["default",{}],["/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-junit/index.js",{"outputDirectory":"<rootDir>/test/reports","outputName":"test-results.xml","suiteName":"Firespoon API Tests","classNameTemplate":"{displayName}.{classname}","titleTemplate":"{title}","ancestorSeparator":" › ","usePathForSuiteName":true}],["/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-html-reporters/index.js",{"publicPath":"<rootDir>/test/reports","filename":"test-report.html","pageTitle":"Firespoon API 测试报告","hideIcon":false,"expand":false,"testCommand":"npm test","openReport":false,"failureMessageOnly":0,"enableMergeData":true,"dataMergeLevel":2,"inlineSource":false}],["/home/<USER>/firespoon/Firespoon_API_TF/test/config/customReporter.js",{"outputFile":"<rootDir>/test/reports/custom-report.json"}],["/home/<USER>/firespoon/Firespoon_API_TF/test/config/detailedLogReporter.js",{}]],"rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","runTestsByPath":false,"seed":693002279,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"test/integration/refund.integration.test.js","testResultsProcessor":"/home/<USER>/firespoon/Firespoon_API_TF/test/config/testResultsProcessor.js","testSequencer":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchPlugins":[{"config":{},"path":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-watch-typeahead/build/file_name_plugin/plugin.js"},{"config":{},"path":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-watch-typeahead/build/test_name_plugin/plugin.js"}],"watchman":true,"workerThreads":false},"endTime":1750687200038,"_reporterOptions":{"publicPath":"/home/<USER>/firespoon/Firespoon_API_TF/test/reports","filename":"test-report.html","expand":false,"pageTitle":"Firespoon API 测试报告","hideIcon":false,"testCommand":"npm test","openReport":false,"failureMessageOnly":0,"enableMergeData":true,"dataMergeLevel":2,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})