[2025-06-23T13:27:41.978Z] ================================================================================
[2025-06-23T13:27:41.978Z] Test Run Started
[2025-06-23T13:27:41.978Z] Timestamp: 2025-06-23T13:27:41.978Z
[2025-06-23T13:27:41.979Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-23T13:27:41.979Z] Runtime Version: Node.js v18.20.5
[2025-06-23T13:27:41.979Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-23T13:27:41.979Z] ================================================================================
[2025-06-23T13:27:42.024Z] [RUN START] - Test execution beginning
[2025-06-23T13:27:42.024Z] Total Test Suites: 14
[2025-06-23T13:27:42.024Z] Test Environment: test
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-23T13:27:42.128Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.128Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-23T13:27:42.129Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.129Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-23T13:27:42.129Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.129Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-23T13:27:42.129Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.129Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-23T13:27:42.129Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:42.129Z] [SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-23T13:27:42.129Z] Suite Display Name: INTEGRATION
[2025-06-23T13:27:47.176Z]   [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-23T13:27:47.177Z]   Module: refund
[2025-06-23T13:27:47.177Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-23T13:27:47.457Z]   [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-23T13:27:47.457Z]   Module: refund
[2025-06-23T13:27:47.457Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-23T13:27:47.458Z]   [Arrange] - Precondition: Test environment prepared for "should verify refundStatus default value is NONE"
[2025-06-23T13:27:47.458Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:47.458Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:47.458Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:47.458Z]   [Act] - Step: Executing test logic for "should verify refundStatus default value is NONE"
[2025-06-23T13:27:47.458Z]   [Act Log] - Loading target module: /test/integration/refund.integration.js
[2025-06-23T13:27:47.459Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:47.459Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:47.459Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:47.459Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:47.459Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:47.459Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:27:47.459Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:27:47.459Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:27:47.459Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:27:47.459Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:27:47.459Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:27:47.459Z]   [CASE END] - Duration: 285ms
[2025-06-23T13:27:47.704Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-23T13:27:47.704Z] Suite Duration: 5192ms
[2025-06-23T13:27:47.704Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T13:27:53.938Z]   [CASE START] - TC-3CAE5E97: should create a new order
[2025-06-23T13:27:53.939Z]   Module: graphql
[2025-06-23T13:27:53.939Z]   Full Path: Order GraphQL Mutations › should create a new order
[2025-06-23T13:27:53.964Z]   [CASE START] - TC-674469B3: should process incoming message and create session
[2025-06-23T13:27:53.964Z]   Module: whatsapp
[2025-06-23T13:27:53.964Z]   Full Path: WhatsApp Webhook Integration › should process incoming message and create session
[2025-06-23T13:27:53.975Z]   [Arrange] - Precondition: Test environment prepared for "should create a new order"
[2025-06-23T13:27:53.975Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:53.975Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:53.975Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:53.975Z]   [Act] - Step: Executing test logic for "should create a new order"
[2025-06-23T13:27:53.975Z]   [Act Log] - Loading target module: /test/integration/graphql/orderMutations.js
[2025-06-23T13:27:53.975Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:53.975Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:53.975Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:53.975Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:53.975Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:53.976Z]   [Act Log] - Exception caught during execution
[2025-06-23T13:27:53.976Z]   [Act Log] - Error type: MongooseError
[2025-06-23T13:27:53.976Z]   [Act Log] - Error message: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:53.976Z]   [Act Log] - Stack trace available: Yes
[2025-06-23T13:27:53.976Z]   [Assert] - Verifying: Expected success but encountered failure
[2025-06-23T13:27:53.976Z]   [Assert Log] - Expected result: Test should pass without errors
[2025-06-23T13:27:53.976Z]   [Assert Log] - Actual result: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:53.976Z]   [Assert Log] - Comparison status: FAILED - Expected !== Actual
[2025-06-23T13:27:53.976Z]   [Assert Log] - Error details: Stack trace: at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
[2025-06-23T13:27:53.976Z]   [RESULT] - FAIL: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:53.976Z]   [CASE END] - Duration: 8ms
[2025-06-23T13:27:53.978Z]   [CASE START] - TC-6385F57E: should fail to create order with invalid restaurant ID
[2025-06-23T13:27:53.980Z]   Module: graphql
[2025-06-23T13:27:53.981Z]   Full Path: Order GraphQL Mutations › should fail to create order with invalid restaurant ID
[2025-06-23T13:27:53.985Z]   [CASE START] - TC-7D51EFC9: should fail to create order without authentication
[2025-06-23T13:27:53.986Z]   Module: graphql
[2025-06-23T13:27:53.987Z]   Full Path: Order GraphQL Mutations › should fail to create order without authentication
[2025-06-23T13:27:53.987Z]   [CASE START] - TC-7912DF71: should update order status
[2025-06-23T13:27:53.987Z]   Module: graphql
[2025-06-23T13:27:53.987Z]   Full Path: Order GraphQL Mutations › should update order status
[2025-06-23T13:27:53.987Z]   [Arrange] - Precondition: Test environment prepared for "should fail to create order with invalid restaurant ID"
[2025-06-23T13:27:53.987Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:53.987Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:53.987Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:53.987Z]   [Act] - Step: Executing test logic for "should fail to create order with invalid restaurant ID"
[2025-06-23T13:27:53.987Z]   [Act Log] - Loading target module: /test/integration/graphql/orderMutations.js
[2025-06-23T13:27:53.987Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:53.987Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:53.988Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:53.988Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:53.988Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:53.988Z]   [Act Log] - Exception caught during execution
[2025-06-23T13:27:53.988Z]   [Act Log] - Error type: MongooseError
[2025-06-23T13:27:53.988Z]   [Act Log] - Error message: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:53.988Z]   [Act Log] - Stack trace available: Yes
[2025-06-23T13:27:53.988Z]   [Assert] - Verifying: Expected success but encountered failure
[2025-06-23T13:27:53.988Z]   [Assert Log] - Expected result: Test should pass without errors
[2025-06-23T13:27:53.988Z]   [Assert Log] - Actual result: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:53.988Z]   [Assert Log] - Comparison status: FAILED - Expected !== Actual
[2025-06-23T13:27:53.988Z]   [Assert Log] - Error details: Stack trace: at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
[2025-06-23T13:27:53.988Z]   [RESULT] - FAIL: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:53.988Z]   [CASE END] - Duration: 2ms
[2025-06-23T13:27:54.009Z]   [CASE START] - TC-432D970F: should reject webhook with invalid signature
[2025-06-23T13:27:54.010Z]   Module: whatsapp
[2025-06-23T13:27:54.010Z]   Full Path: WhatsApp Webhook Integration › should reject webhook with invalid signature
[2025-06-23T13:27:54.011Z]   [CASE START] - TC-3376EAD8: should handle malformed webhook payload
[2025-06-23T13:27:54.011Z]   Module: whatsapp
[2025-06-23T13:27:54.011Z]   Full Path: WhatsApp Webhook Integration › should handle malformed webhook payload
[2025-06-23T13:27:54.011Z]   [Arrange] - Precondition: Test environment prepared for "should reject webhook with invalid signature"
[2025-06-23T13:27:54.011Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:54.012Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:54.012Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:54.012Z]   [Act] - Step: Executing test logic for "should reject webhook with invalid signature"
[2025-06-23T13:27:54.012Z]   [Act Log] - Loading target module: /test/integration/whatsapp/webhook.js
[2025-06-23T13:27:54.012Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:54.012Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:54.012Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:54.012Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:54.012Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:54.012Z]   [Act Log] - Exception caught during execution
[2025-06-23T13:27:54.012Z]   [Act Log] - Error type: MongooseError
[2025-06-23T13:27:54.012Z]   [Act Log] - Error message: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:54.012Z]   [Act Log] - Stack trace available: Yes
[2025-06-23T13:27:54.012Z]   [Assert] - Verifying: Expected success but encountered failure
[2025-06-23T13:27:54.012Z]   [Assert Log] - Expected result: Test should pass without errors
[2025-06-23T13:27:54.012Z]   [Assert Log] - Actual result: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:54.012Z]   [Assert Log] - Comparison status: FAILED - Expected !== Actual
[2025-06-23T13:27:54.012Z]   [Assert Log] - Error details: Stack trace: at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
[2025-06-23T13:27:54.012Z]   [RESULT] - FAIL: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
[2025-06-23T13:27:54.012Z]   [CASE END] - Duration: 1ms
[2025-06-23T13:27:54.151Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-23T13:27:54.151Z] Suite Duration: 11722ms
[2025-06-23T13:27:54.151Z] Suite Results: 0 passed, 4 failed, 0 skipped
[2025-06-23T13:27:54.220Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-23T13:27:54.220Z] Suite Duration: 11750ms
[2025-06-23T13:27:54.220Z] Suite Results: 0 passed, 3 failed, 0 skipped
[2025-06-23T13:27:59.162Z]   [CASE START] - TC-2E236918: should create real payment intent with Stripe
[2025-06-23T13:27:59.162Z]   Module: real
[2025-06-23T13:27:59.162Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should create real payment intent with Stripe
[2025-06-23T13:27:59.694Z]   [CASE START] - TC-18AE2E8F: should retrieve real payment intent status
[2025-06-23T13:27:59.696Z]   Module: real
[2025-06-23T13:27:59.696Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should retrieve real payment intent status
[2025-06-23T13:27:59.696Z]   [Arrange] - Precondition: Test environment prepared for "should create real payment intent with Stripe"
[2025-06-23T13:27:59.696Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:59.696Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:59.696Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:59.696Z]   [Act] - Step: Executing test logic for "should create real payment intent with Stripe"
[2025-06-23T13:27:59.696Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.real.js
[2025-06-23T13:27:59.696Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:59.696Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:59.696Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:59.696Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:59.696Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:59.696Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:27:59.697Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:27:59.697Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:27:59.697Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:27:59.697Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:27:59.697Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:27:59.697Z]   [CASE END] - Duration: 540ms
[2025-06-23T13:27:59.786Z]   [CASE START] - TC-6AADE1B7: should handle payment with test card
[2025-06-23T13:27:59.786Z]   Module: real
[2025-06-23T13:27:59.786Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle payment with test card
[2025-06-23T13:27:59.787Z]   [Arrange] - Precondition: Test environment prepared for "should retrieve real payment intent status"
[2025-06-23T13:27:59.787Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:59.787Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:59.787Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:59.787Z]   [Act] - Step: Executing test logic for "should retrieve real payment intent status"
[2025-06-23T13:27:59.787Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.real.js
[2025-06-23T13:27:59.787Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:59.787Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:59.787Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:59.787Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:59.787Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:59.787Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:27:59.787Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:27:59.787Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:27:59.787Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:27:59.787Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:27:59.787Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:27:59.787Z]   [CASE END] - Duration: 89ms
[2025-06-23T13:27:59.849Z]   [CASE START] - TC-54B75689: should handle declined card
[2025-06-23T13:27:59.849Z]   Module: real
[2025-06-23T13:27:59.850Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle declined card
[2025-06-23T13:27:59.850Z]   [Arrange] - Precondition: Test environment prepared for "should handle payment with test card"
[2025-06-23T13:27:59.850Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:59.850Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:59.850Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:59.850Z]   [Act] - Step: Executing test logic for "should handle payment with test card"
[2025-06-23T13:27:59.850Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.real.js
[2025-06-23T13:27:59.850Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:59.850Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:59.850Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:59.850Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:59.850Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:59.850Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:27:59.850Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:27:59.850Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:27:59.850Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:27:59.850Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:27:59.850Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:27:59.850Z]   [CASE END] - Duration: 64ms
[2025-06-23T13:27:59.911Z]   [CASE START] - TC-332A1C6D: should create real Stripe customer
[2025-06-23T13:27:59.911Z]   Module: real
[2025-06-23T13:27:59.911Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should create real Stripe customer
[2025-06-23T13:27:59.957Z]   [CASE START] - TC-164683AD: should retrieve real Stripe customer
[2025-06-23T13:27:59.957Z]   Module: real
[2025-06-23T13:27:59.957Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should retrieve real Stripe customer
[2025-06-23T13:27:59.957Z]   [Arrange] - Precondition: Test environment prepared for "should create real Stripe customer"
[2025-06-23T13:27:59.957Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:59.957Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:59.957Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:59.958Z]   [Act] - Step: Executing test logic for "should create real Stripe customer"
[2025-06-23T13:27:59.958Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.real.js
[2025-06-23T13:27:59.958Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:59.958Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:59.958Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:59.958Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:59.958Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:59.958Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:27:59.958Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:27:59.958Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:27:59.958Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:27:59.958Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:27:59.958Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:27:59.958Z]   [CASE END] - Duration: 45ms
[2025-06-23T13:27:59.996Z]   [CASE START] - TC-5FD44F7E: should process real webhook signature validation
[2025-06-23T13:27:59.996Z]   Module: real
[2025-06-23T13:27:59.996Z]   Full Path: Real Stripe Payment Integration Tests › Real Webhook Processing › should process real webhook signature validation
[2025-06-23T13:27:59.997Z]   [Arrange] - Precondition: Test environment prepared for "should retrieve real Stripe customer"
[2025-06-23T13:27:59.997Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:27:59.997Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:27:59.997Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:27:59.997Z]   [Act] - Step: Executing test logic for "should retrieve real Stripe customer"
[2025-06-23T13:27:59.997Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.real.js
[2025-06-23T13:27:59.997Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:27:59.997Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:27:59.997Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:27:59.997Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:27:59.997Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:27:59.997Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:27:59.997Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:27:59.997Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:27:59.997Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:27:59.997Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:27:59.997Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:27:59.997Z]   [CASE END] - Duration: 40ms
[2025-06-23T13:28:00.045Z]   [CASE START] - TC-74FBC87B: should handle invalid amount
[2025-06-23T13:28:00.045Z]   Module: real
[2025-06-23T13:28:00.045Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid amount
[2025-06-23T13:28:00.115Z]   [CASE START] - TC-56B8BFFE: should handle invalid currency
[2025-06-23T13:28:00.115Z]   Module: real
[2025-06-23T13:28:00.115Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid currency
[2025-06-23T13:28:00.115Z]   [Arrange] - Precondition: Test environment prepared for "should handle invalid amount"
[2025-06-23T13:28:00.115Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.115Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.115Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.115Z]   [Act] - Step: Executing test logic for "should handle invalid amount"
[2025-06-23T13:28:00.115Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.real.js
[2025-06-23T13:28:00.115Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.115Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.115Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.115Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.115Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.116Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.116Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.116Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.116Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.116Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.116Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.116Z]   [CASE END] - Duration: 68ms
[2025-06-23T13:28:00.282Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-23T13:28:00.283Z] Suite Duration: 17802ms
[2025-06-23T13:28:00.283Z] Suite Results: 9 passed, 0 failed, 0 skipped
[2025-06-23T13:28:00.441Z]   [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-23T13:28:00.442Z]   Module: graphql
[2025-06-23T13:28:00.442Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-23T13:28:00.442Z]   [CASE START] - TC-387C915A: should respond to GraphQL endpoint
[2025-06-23T13:28:00.442Z]   Module: graphql
[2025-06-23T13:28:00.442Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should respond to GraphQL endpoint
[2025-06-23T13:28:00.449Z]   [CASE START] - TC-50629841: should have updateOrderStatus mutation in schema
[2025-06-23T13:28:00.449Z]   Module: graphql
[2025-06-23T13:28:00.449Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-23T13:28:00.449Z]   [CASE START] - TC-528B5677: should have payment related types in schema
[2025-06-23T13:28:00.449Z]   Module: graphql
[2025-06-23T13:28:00.449Z]   Full Path: Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-23T13:28:00.454Z]   [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-23T13:28:00.454Z]   Module: order
[2025-06-23T13:28:00.454Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-23T13:28:00.457Z]   [CASE START] - TC-7DB19ADA: should have order subscription in schema
[2025-06-23T13:28:00.457Z]   Module: graphql
[2025-06-23T13:28:00.457Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have order subscription in schema
[2025-06-23T13:28:00.460Z]   [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-23T13:28:00.460Z]   Module: graphql
[2025-06-23T13:28:00.460Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-23T13:28:00.463Z]   [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-23T13:28:00.463Z]   Module: graphql
[2025-06-23T13:28:00.463Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-23T13:28:00.739Z]   [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-23T13:28:00.740Z]   Module: graphql
[2025-06-23T13:28:00.740Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-23T13:28:00.740Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-23T13:28:00.740Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.740Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.740Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.740Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-23T13:28:00.740Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-23T13:28:00.741Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.741Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.741Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.741Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.741Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.741Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.741Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.741Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.741Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.741Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.741Z]   [Assert Log] - Expected: Object should have property "payment"
[2025-06-23T13:28:00.742Z]   [Assert Log] - Actual: object.hasOwnProperty("payment") = true
[2025-06-23T13:28:00.742Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("payment") - PASSED
[2025-06-23T13:28:00.742Z]   [Assert Log] - Variable: object.payment = [Function] or [Object]
[2025-06-23T13:28:00.742Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.742Z]   [CASE END] - Duration: 280ms
[2025-06-23T13:28:00.744Z]   [CASE START] - TC-7B992587: should have mutation types in schema
[2025-06-23T13:28:00.744Z]   Module: graphql
[2025-06-23T13:28:00.744Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have mutation types in schema
[2025-06-23T13:28:00.745Z]   [Arrange] - Precondition: Test environment prepared for "should have order subscription in schema"
[2025-06-23T13:28:00.745Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.745Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.745Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.745Z]   [Act] - Step: Executing test logic for "should have order subscription in schema"
[2025-06-23T13:28:00.745Z]   [Act Log] - Loading target module: /test/integration/order/orderNotifications.js
[2025-06-23T13:28:00.745Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.745Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.745Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.745Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.745Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.745Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.745Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.745Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.745Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.745Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.745Z]   [Assert Log] - Expected: Object should have property "order"
[2025-06-23T13:28:00.745Z]   [Assert Log] - Actual: object.hasOwnProperty("order") = true
[2025-06-23T13:28:00.745Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("order") - PASSED
[2025-06-23T13:28:00.745Z]   [Assert Log] - Variable: object.order = [Function] or [Object]
[2025-06-23T13:28:00.745Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.745Z]   [CASE END] - Duration: 289ms
[2025-06-23T13:28:00.746Z]   [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-23T13:28:00.747Z]   Module: graphql
[2025-06-23T13:28:00.747Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-23T13:28:00.747Z]   [Arrange] - Precondition: Test environment prepared for "should have Customer type in schema"
[2025-06-23T13:28:00.747Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.747Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.747Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.747Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-23T13:28:00.747Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-23T13:28:00.747Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.747Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.747Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.747Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.747Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.747Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.748Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.748Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.748Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.748Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.748Z]   [Assert Log] - Expected: Object should have property "Customer"
[2025-06-23T13:28:00.748Z]   [Assert Log] - Actual: object.hasOwnProperty("Customer") = true
[2025-06-23T13:28:00.748Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Customer") - PASSED
[2025-06-23T13:28:00.748Z]   [Assert Log] - Variable: object.Customer = [Function] or [Object]
[2025-06-23T13:28:00.748Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.748Z]   [CASE END] - Duration: 314ms
[2025-06-23T13:28:00.787Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-23T13:28:00.788Z] Suite Duration: 18372ms
[2025-06-23T13:28:00.788Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-23T13:28:00.791Z]   [CASE START] - TC-21545EE6: should handle invalid GraphQL queries
[2025-06-23T13:28:00.791Z]   Module: graphql
[2025-06-23T13:28:00.791Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should handle invalid GraphQL queries
[2025-06-23T13:28:00.792Z]   [Arrange] - Precondition: Test environment prepared for "should respond to GraphQL endpoint"
[2025-06-23T13:28:00.792Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.793Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.793Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.793Z]   [Act] - Step: Executing test logic for "should respond to GraphQL endpoint"
[2025-06-23T13:28:00.793Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-23T13:28:00.793Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.793Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.793Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.793Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.793Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.793Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.793Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.793Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.793Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.793Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.793Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.793Z]   [CASE END] - Duration: 315ms
[2025-06-23T13:28:00.795Z]   [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-23T13:28:00.795Z]   Module: order
[2025-06-23T13:28:00.795Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-23T13:28:00.796Z]   [Arrange] - Precondition: Test environment prepared for "should have order query in schema"
[2025-06-23T13:28:00.796Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.796Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.796Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.796Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-23T13:28:00.796Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-23T13:28:00.796Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.796Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.796Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.796Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.796Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.796Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.796Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.796Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.796Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.796Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.796Z]   [Assert Log] - Expected: Object should have property "order"
[2025-06-23T13:28:00.796Z]   [Assert Log] - Actual: object.hasOwnProperty("order") = true
[2025-06-23T13:28:00.796Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("order") - PASSED
[2025-06-23T13:28:00.796Z]   [Assert Log] - Variable: object.order = [Function] or [Object]
[2025-06-23T13:28:00.796Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.796Z]   [CASE END] - Duration: 296ms
[2025-06-23T13:28:00.797Z]   [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-23T13:28:00.797Z]   Module: graphql
[2025-06-23T13:28:00.797Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-23T13:28:00.798Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-23T13:28:00.798Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.798Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.798Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.798Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-23T13:28:00.798Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-23T13:28:00.798Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.798Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.798Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.798Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.798Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.798Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.798Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.798Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.798Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.798Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.798Z]   [Assert Log] - Expected: Object should have property "payment"
[2025-06-23T13:28:00.798Z]   [Assert Log] - Actual: object.hasOwnProperty("payment") = true
[2025-06-23T13:28:00.798Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("payment") - PASSED
[2025-06-23T13:28:00.798Z]   [Assert Log] - Variable: object.payment = [Function] or [Object]
[2025-06-23T13:28:00.798Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.798Z]   [CASE END] - Duration: 293ms
[2025-06-23T13:28:00.799Z]   [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-23T13:28:00.799Z]   Module: graphql
[2025-06-23T13:28:00.799Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-23T13:28:00.800Z]   [Arrange] - Precondition: Test environment prepared for "should have updateOrderStatus mutation in schema"
[2025-06-23T13:28:00.800Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.800Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.800Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.800Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-23T13:28:00.800Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-23T13:28:00.800Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.800Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.800Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.800Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.800Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.800Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.800Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.800Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.800Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.800Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.800Z]   [Assert Log] - Expected: Object should have property "updateOrderStatus"
[2025-06-23T13:28:00.800Z]   [Assert Log] - Actual: object.hasOwnProperty("updateOrderStatus") = true
[2025-06-23T13:28:00.800Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("updateOrderStatus") - PASSED
[2025-06-23T13:28:00.800Z]   [Assert Log] - Variable: object.updateOrderStatus = [Function] or [Object]
[2025-06-23T13:28:00.800Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.800Z]   [CASE END] - Duration: 319ms
[2025-06-23T13:28:00.820Z]   [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-23T13:28:00.820Z]   Module: graphql
[2025-06-23T13:28:00.820Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-23T13:28:00.821Z]   [Arrange] - Precondition: Test environment prepared for "should have Address type in schema"
[2025-06-23T13:28:00.821Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.821Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.821Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.821Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-23T13:28:00.821Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-23T13:28:00.821Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.821Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.821Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.821Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.821Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.821Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.821Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.821Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.821Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.821Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.821Z]   [Assert Log] - Expected: Object should have property "Address"
[2025-06-23T13:28:00.821Z]   [Assert Log] - Actual: object.hasOwnProperty("Address") = true
[2025-06-23T13:28:00.821Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Address") - PASSED
[2025-06-23T13:28:00.821Z]   [Assert Log] - Variable: object.Address = [Function] or [Object]
[2025-06-23T13:28:00.821Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.821Z]   [CASE END] - Duration: 70ms
[2025-06-23T13:28:00.825Z]   [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-23T13:28:00.825Z]   Module: graphql
[2025-06-23T13:28:00.826Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-23T13:28:00.826Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type with orderStatus field in schema"
[2025-06-23T13:28:00.826Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:00.826Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:00.826Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:00.826Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-23T13:28:00.826Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-23T13:28:00.826Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:00.826Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:00.826Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:00.826Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:00.827Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:00.827Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:00.827Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:00.827Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:00.827Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:00.827Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:00.827Z]   [Assert Log] - Expected: Object should have property "Order"
[2025-06-23T13:28:00.827Z]   [Assert Log] - Actual: object.hasOwnProperty("Order") = true
[2025-06-23T13:28:00.827Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Order") - PASSED
[2025-06-23T13:28:00.827Z]   [Assert Log] - Variable: object.Order = [Function] or [Object]
[2025-06-23T13:28:00.827Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:00.827Z]   [CASE END] - Duration: 75ms
[2025-06-23T13:28:00.872Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-23T13:28:00.872Z] Suite Duration: 18460ms
[2025-06-23T13:28:00.872Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T13:28:00.925Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-23T13:28:00.925Z] Suite Duration: 18446ms
[2025-06-23T13:28:00.925Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T13:28:00.973Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-23T13:28:00.973Z] Suite Duration: 18465ms
[2025-06-23T13:28:00.973Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T13:28:01.018Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-23T13:28:01.018Z] Suite Duration: 18480ms
[2025-06-23T13:28:01.018Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T13:28:01.021Z]   [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-23T13:28:01.021Z]   Module: graphql
[2025-06-23T13:28:01.021Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-23T13:28:01.021Z]   [CASE START] - TC-18BF1AAD: should handle simple queries
[2025-06-23T13:28:01.021Z]   Module: graphql
[2025-06-23T13:28:01.021Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle simple queries
[2025-06-23T13:28:01.022Z]   [CASE START] - TC-14898FE1: should validate GraphQL syntax
[2025-06-23T13:28:01.022Z]   Module: graphql
[2025-06-23T13:28:01.022Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should validate GraphQL syntax
[2025-06-23T13:28:01.022Z]   [CASE START] - TC-63BF36C2: should handle empty queries
[2025-06-23T13:28:01.022Z]   Module: graphql
[2025-06-23T13:28:01.022Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle empty queries
[2025-06-23T13:28:01.022Z]   [Arrange] - Precondition: Test environment prepared for "should handle invalid GraphQL queries"
[2025-06-23T13:28:01.023Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:01.023Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:01.023Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:01.023Z]   [Act] - Step: Executing test logic for "should handle invalid GraphQL queries"
[2025-06-23T13:28:01.023Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-23T13:28:01.023Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:01.023Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:01.023Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:01.023Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:01.023Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:01.023Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:01.023Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:01.023Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:01.023Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:01.023Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:01.023Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:01.023Z]   [CASE END] - Duration: 114ms
[2025-06-23T13:28:01.030Z]   [CASE START] - TC-2C17DA72: should validate customer-related mutations exist
[2025-06-23T13:28:01.030Z]   Module: graphql
[2025-06-23T13:28:01.030Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should validate customer-related mutations exist
[2025-06-23T13:28:01.030Z]   [CASE START] - TC-D206B8: should handle GraphQL validation errors
[2025-06-23T13:28:01.030Z]   Module: graphql
[2025-06-23T13:28:01.030Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should handle GraphQL validation errors
[2025-06-23T13:28:01.031Z]   [Arrange] - Precondition: Test environment prepared for "should have AddressInput type in schema"
[2025-06-23T13:28:01.031Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:01.031Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:01.031Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:01.031Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-23T13:28:01.031Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-23T13:28:01.031Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:01.031Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:01.031Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:01.031Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:01.031Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:01.031Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:01.031Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:01.031Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:01.031Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:01.031Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:01.032Z]   [Assert Log] - Expected: Object should have property "AddressInput"
[2025-06-23T13:28:01.032Z]   [Assert Log] - Actual: object.hasOwnProperty("AddressInput") = true
[2025-06-23T13:28:01.032Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("AddressInput") - PASSED
[2025-06-23T13:28:01.032Z]   [Assert Log] - Variable: object.AddressInput = [Function] or [Object]
[2025-06-23T13:28:01.032Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:01.032Z]   [CASE END] - Duration: 69ms
[2025-06-23T13:28:01.105Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-23T13:28:01.105Z] Suite Duration: 18668ms
[2025-06-23T13:28:01.105Z] Suite Results: 5 passed, 0 failed, 0 skipped
[2025-06-23T13:28:01.156Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-23T13:28:01.156Z] Suite Duration: 18513ms
[2025-06-23T13:28:01.156Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-23T13:28:01.158Z]   [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-23T13:28:01.158Z]   Module: graphql
[2025-06-23T13:28:01.159Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-23T13:28:01.159Z]   [CASE START] - TC-6C7A3797: should list available queries
[2025-06-23T13:28:01.159Z]   Module: graphql
[2025-06-23T13:28:01.159Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-23T13:28:01.159Z]   [Arrange] - Precondition: Test environment prepared for "should handle empty queries"
[2025-06-23T13:28:01.159Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:01.159Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:01.159Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:01.159Z]   [Act] - Step: Executing test logic for "should handle empty queries"
[2025-06-23T13:28:01.159Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-23T13:28:01.159Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:01.160Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:01.160Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:01.160Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:01.160Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:01.160Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:01.160Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:01.160Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:01.160Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:01.160Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:01.160Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:01.160Z]   [CASE END] - Duration: 59ms
[2025-06-23T13:28:01.162Z]   [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-23T13:28:01.162Z]   Module: graphql
[2025-06-23T13:28:01.162Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-23T13:28:01.162Z]   [Arrange] - Precondition: Test environment prepared for "should list available queries"
[2025-06-23T13:28:01.162Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:01.162Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:01.162Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:01.162Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-23T13:28:01.162Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-23T13:28:01.162Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:01.162Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:01.162Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:01.163Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:01.163Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:01.163Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:01.163Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:01.163Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:01.163Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:01.163Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:01.163Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:01.163Z]   [CASE END] - Duration: 47ms
[2025-06-23T13:28:01.332Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-23T13:28:01.332Z] Suite Duration: 18832ms
[2025-06-23T13:28:01.332Z] Suite Results: 9 passed, 0 failed, 0 skipped
[2025-06-23T13:28:02.447Z]   [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-23T13:28:02.447Z]   Module: graphql
[2025-06-23T13:28:02.447Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-23T13:28:02.449Z]   [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-23T13:28:02.449Z]   Module: graphql
[2025-06-23T13:28:02.449Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-23T13:28:02.552Z]   [Arrange] - Precondition: Test environment prepared for "should respond to GraphQL introspection query"
[2025-06-23T13:28:02.552Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:02.552Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:02.552Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:02.552Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-23T13:28:02.552Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-23T13:28:02.552Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:02.552Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:02.552Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:02.552Z]   [Act Log] - Captured 26 program outputs during execution
[2025-06-23T13:28:02.552Z]   [Act Log] - process: 26 outputs
[2025-06-23T13:28:02.552Z]   [Act Log] - === Program Output Details ===
[2025-06-23T13:28:02.552Z]   [Act Log] - +61ms [process.stderr] HTTP [33m3985639[39m: call onSocket [33m0[39m [33m0[39m

[2025-06-23T13:28:02.552Z]   [Act Log] - +62ms [process.stderr] HTTP [33m3985633[39m: call onSocket [33m0[39m [33m0[39m

[2025-06-23T13:28:02.552Z]   [Act Log] - +62ms [process.stderr] HTTP [33m3985639[39m: createConnection 127.0.0.1:45659: [Object: null prototype] {
  method: [32m'POST'[39m,
  port: [32m'45659'[39m,
  path: [1mnull[22m,
  host: [32m'127.0.0.1'[39m,
  ca: [90mundefined[39m,
  key: [90mundefined[39m,
  pfx: [90mundefined[39m,
  cert: [90mundefined[39m,
  passphrase: [90mundefined[39m,
  agent: [33mfalse[39m,
  lookup: [90mundefined[39m,
  rejectUnauthorized: [33mtrue[39m,
  noDelay: [33mtrue[39m,
  servername: [32m''[39m,
  _agentKey: [32m'127.0.0.1:45659:'[39m
}

[2025-06-23T13:28:02.552Z]   [Act Log] - +63ms [process.stderr] HTTP [33m3985639[39m: sockets 127.0.0.1:45659: [33m1[39m [33m1[39m

[2025-06-23T13:28:02.552Z]   [Act Log] - +63ms [process.stderr] HTTP [33m3985633[39m: createConnection 127.0.0.1:33777: [Object: null prototype] {
  method: [32m'POST'[39m,
  port: [32m'33777'[39m,
  path: [1mnull[22m,
  host: [32m'127.0.0.1'[39m,
  ca: [90mundefined[39m,
  key: [90mundefined[39m,
  pfx: [90mundefined[39m,
  cert: [90mundefined[39m,
  passphrase: [90mundefined[39m,
  agent: [33mfalse[39m,
  lookup: [90mundefined[39m,
  rejectUnauthorized: [33mtrue[39m,
  noDelay: [33mtrue[39m,
  servername: [32m''[39m,
  _agentKey: [32m'127.0.0.1:33777:'[39m
}

[2025-06-23T13:28:02.552Z]   [Act Log] - +63ms [process.stderr] HTTP [33m3985633[39m: sockets 127.0.0.1:33777: [33m1[39m [33m1[39m

[2025-06-23T13:28:02.552Z]   [Act Log] - +64ms [process.stderr] HTTP [33m3985639[39m: write ret = true

[2025-06-23T13:28:02.552Z]   [Act Log] - +64ms [process.stderr] HTTP [33m3985639[39m: outgoing message end.

[2025-06-23T13:28:02.552Z]   [Act Log] - +65ms [process.stderr] (node:3985639) Warning: Setting the NODE_DEBUG environment variable to 'http' can expose sensitive data (such as passwords, tokens and authentication headers) in the resulting log.

[2025-06-23T13:28:02.552Z]   [Act Log] - +65ms [process.stderr] HTTP [33m3985633[39m: write ret = true

[2025-06-23T13:28:02.552Z]   [Act Log] - +65ms [process.stderr] HTTP [33m3985633[39m: outgoing message end.

[2025-06-23T13:28:02.552Z]   [Act Log] - +65ms [process.stderr] (node:3985633) Warning: Setting the NODE_DEBUG environment variable to 'http' can expose sensitive data (such as passwords, tokens and authentication headers) in the resulting log.

[2025-06-23T13:28:02.552Z]   [Act Log] - +66ms [process.stderr] HTTP [33m3985639[39m: SERVER new http connection

[2025-06-23T13:28:02.552Z]   [Act Log] - +67ms [process.stderr] HTTP [33m3985633[39m: SERVER new http connection

[2025-06-23T13:28:02.552Z]   [Act Log] - +76ms [process.stderr] HTTP [33m3985639[39m: SERVER socketOnParserExecute 365

[2025-06-23T13:28:02.552Z]   [Act Log] - +78ms [process.stderr] HTTP [33m3985633[39m: SERVER socketOnParserExecute 435

[2025-06-23T13:28:02.552Z]   [Act Log] - +91ms [process.stderr] HTTP [33m3985639[39m: write ret = true

[2025-06-23T13:28:02.552Z]   [Act Log] - +92ms [process.stderr] HTTP [33m3985639[39m: outgoing message end.

[2025-06-23T13:28:02.552Z]   [Act Log] - +92ms [process.stderr] HTTP [33m3985639[39m: server socket close

[2025-06-23T13:28:02.552Z]   [Act Log] - +93ms [process.stderr] HTTP [33m3985633[39m: write ret = true

[2025-06-23T13:28:02.552Z]   [Act Log] - +93ms [process.stderr] HTTP [33m3985633[39m: outgoing message end.

[2025-06-23T13:28:02.552Z]   [Act Log] - +93ms [process.stderr] HTTP [33m3985639[39m: AGENT incoming response!

[2025-06-23T13:28:02.552Z]   [Act Log] - +94ms [process.stderr] HTTP [33m3985639[39m: AGENT socket.destroySoon()

[2025-06-23T13:28:02.552Z]   [Act Log] - +94ms [process.stderr] HTTP [33m3985633[39m: server socket close

[2025-06-23T13:28:02.552Z]   [Act Log] - +95ms [process.stderr] HTTP [33m3985633[39m: AGENT incoming response!

[2025-06-23T13:28:02.552Z]   [Act Log] - +96ms [process.stderr] HTTP [33m3985633[39m: AGENT socket.destroySoon()

[2025-06-23T13:28:02.552Z]   [Act Log] - === End Program Output ===
[2025-06-23T13:28:02.552Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:02.552Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:02.553Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:02.553Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:02.553Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:02.553Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:02.553Z]   [CASE END] - Duration: 106ms
[2025-06-23T13:28:02.553Z]   [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-23T13:28:02.553Z]   Module: graphql
[2025-06-23T13:28:02.553Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-23T13:28:02.553Z]   [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-23T13:28:02.553Z]   Module: graphql
[2025-06-23T13:28:02.553Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-23T13:28:02.554Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type in schema"
[2025-06-23T13:28:02.554Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:02.554Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:02.554Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:02.554Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-23T13:28:02.554Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-23T13:28:02.554Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:02.554Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:02.554Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:02.554Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:02.554Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:02.554Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:02.554Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:02.554Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:02.554Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:02.554Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:02.554Z]   [Assert Log] - Expected: Object should have property "Order"
[2025-06-23T13:28:02.554Z]   [Assert Log] - Actual: object.hasOwnProperty("Order") = true
[2025-06-23T13:28:02.554Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Order") - PASSED
[2025-06-23T13:28:02.554Z]   [Assert Log] - Variable: object.Order = [Function] or [Object]
[2025-06-23T13:28:02.554Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:02.554Z]   [CASE END] - Duration: 106ms
[2025-06-23T13:28:02.588Z]   [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-23T13:28:02.588Z]   Module: graphql
[2025-06-23T13:28:02.588Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-23T13:28:02.631Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-23T13:28:02.631Z] Suite Duration: 20228ms
[2025-06-23T13:28:02.631Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-23T13:28:02.632Z]   [CASE START] - TC-1EE21EDD: should validate GraphQL order operations
[2025-06-23T13:28:02.632Z]   Module: graphql
[2025-06-23T13:28:02.632Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should validate GraphQL order operations
[2025-06-23T13:28:02.632Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderInput type in schema"
[2025-06-23T13:28:02.632Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:02.632Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:02.632Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:02.632Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-23T13:28:02.632Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-23T13:28:02.633Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:02.633Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:02.633Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:02.633Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-23T13:28:02.633Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-23T13:28:02.633Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:02.633Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:02.633Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:02.633Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:02.633Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:02.633Z]   [Assert Log] - Expected: Object should have property "OrderInput"
[2025-06-23T13:28:02.633Z]   [Assert Log] - Actual: object.hasOwnProperty("OrderInput") = true
[2025-06-23T13:28:02.633Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("OrderInput") - PASSED
[2025-06-23T13:28:02.633Z]   [Assert Log] - Variable: object.OrderInput = [Function] or [Object]
[2025-06-23T13:28:02.633Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:02.633Z]   [CASE END] - Duration: 35ms
[2025-06-23T13:28:02.674Z]   [CASE START] - TC-8227B16: should handle malformed order queries
[2025-06-23T13:28:02.675Z]   Module: graphql
[2025-06-23T13:28:02.675Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should handle malformed order queries
[2025-06-23T13:28:02.720Z]   [Arrange] - Precondition: Test environment prepared for "should handle malformed order queries"
[2025-06-23T13:28:02.720Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:02.720Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:02.720Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:02.720Z]   [Act] - Step: Executing test logic for "should handle malformed order queries"
[2025-06-23T13:28:02.720Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-23T13:28:02.720Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:02.720Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:02.720Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:02.720Z]   [Act Log] - Captured 12 program outputs during execution
[2025-06-23T13:28:02.721Z]   [Act Log] - process: 12 outputs
[2025-06-23T13:28:02.721Z]   [Act Log] - === Program Output Details ===
[2025-06-23T13:28:02.721Z]   [Act Log] - +1ms [process.stderr] HTTP [33m3985633[39m: CLIENT socket onClose

[2025-06-23T13:28:02.721Z]   [Act Log] - +2ms [process.stderr] HTTP [33m3985633[39m: removeSocket 127.0.0.1:40285: writable: [33mfalse[39m
HTTP [33m3985633[39m: HTTP socket close

[2025-06-23T13:28:02.721Z]   [Act Log] - +22ms [process.stderr] HTTP [33m3985633[39m: call onSocket [33m0[39m [33m0[39m

[2025-06-23T13:28:02.721Z]   [Act Log] - +22ms [process.stderr] HTTP [33m3985633[39m: createConnection 127.0.0.1:43043: [Object: null prototype] {
  method: [32m'POST'[39m,
  port: [32m'43043'[39m,
  path: [1mnull[22m,
  host: [32m'127.0.0.1'[39m,
  ca: [90mundefined[39m,
  key: [90mundefined[39m,
  pfx: [90mundefined[39m,
  cert: [90mundefined[39m,
  passphrase: [90mundefined[39m,
  agent: [33mfalse[39m,
  lookup: [90mundefined[39m,
  rejectUnauthorized: [33mtrue[39m,
  noDelay: [33mtrue[39m,
  servername: [32m''[39m,
  _agentKey: [32m'127.0.0.1:43043:'[39m
}

[2025-06-23T13:28:02.721Z]   [Act Log] - +22ms [process.stderr] HTTP [33m3985633[39m: sockets 127.0.0.1:43043: [33m1[39m [33m1[39m

[2025-06-23T13:28:02.721Z]   [Act Log] - +22ms [process.stderr] HTTP [33m3985633[39m: write ret = true
HTTP [33m3985633[39m: outgoing message end.

[2025-06-23T13:28:02.721Z]   [Act Log] - +23ms [process.stderr] HTTP [33m3985633[39m: SERVER new http connection

[2025-06-23T13:28:02.721Z]   [Act Log] - +24ms [process.stderr] HTTP [33m3985633[39m: SERVER socketOnParserExecute 283

[2025-06-23T13:28:02.721Z]   [Act Log] - +41ms [process.stderr] HTTP [33m3985633[39m: write ret = true

[2025-06-23T13:28:02.721Z]   [Act Log] - +41ms [process.stderr] HTTP [33m3985633[39m: outgoing message end.
HTTP [33m3985633[39m: server socket close

[2025-06-23T13:28:02.721Z]   [Act Log] - +42ms [process.stderr] HTTP [33m3985633[39m: AGENT incoming response!

[2025-06-23T13:28:02.721Z]   [Act Log] - +42ms [process.stderr] HTTP [33m3985633[39m: AGENT socket.destroySoon()

[2025-06-23T13:28:02.721Z]   [Act Log] - === End Program Output ===
[2025-06-23T13:28:02.721Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:02.721Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:02.721Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:02.721Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:02.721Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:02.721Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:02.721Z]   [CASE END] - Duration: 45ms
[2025-06-23T13:28:02.722Z]   [CASE START] - TC-C2E23B4: should validate required arguments
[2025-06-23T13:28:02.722Z]   Module: graphql
[2025-06-23T13:28:02.722Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should validate required arguments
[2025-06-23T13:28:02.752Z]   [Arrange] - Precondition: Test environment prepared for "should validate required arguments"
[2025-06-23T13:28:02.752Z]   [Arrange Log] - Setting up test environment variables
[2025-06-23T13:28:02.752Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-23T13:28:02.753Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-23T13:28:02.753Z]   [Act] - Step: Executing test logic for "should validate required arguments"
[2025-06-23T13:28:02.753Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-23T13:28:02.753Z]   [Act Log] - Instantiating service/class under test
[2025-06-23T13:28:02.753Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-23T13:28:02.753Z]   [Act Log] - Invoking target method or function
[2025-06-23T13:28:02.753Z]   [Act Log] - Captured 11 program outputs during execution
[2025-06-23T13:28:02.753Z]   [Act Log] - process: 11 outputs
[2025-06-23T13:28:02.753Z]   [Act Log] - === Program Output Details ===
[2025-06-23T13:28:02.753Z]   [Act Log] - +0ms [process.stderr] HTTP [33m3985633[39m: CLIENT socket onClose
HTTP [33m3985633[39m: removeSocket 127.0.0.1:43043: writable: [33mfalse[39m
HTTP [33m3985633[39m: HTTP socket close

[2025-06-23T13:28:02.753Z]   [Act Log] - +23ms [process.stderr] HTTP [33m3985633[39m: call onSocket [33m0[39m [33m0[39m

[2025-06-23T13:28:02.753Z]   [Act Log] - +23ms [process.stderr] HTTP [33m3985633[39m: createConnection 127.0.0.1:38201: [Object: null prototype] {
  method: [32m'POST'[39m,
  port: [32m'38201'[39m,
  path: [1mnull[22m,
  host: [32m'127.0.0.1'[39m,
  ca: [90mundefined[39m,
  key: [90mundefined[39m,
  pfx: [90mundefined[39m,
  cert: [90mundefined[39m,
  passphrase: [90mundefined[39m,
  agent: [33mfalse[39m,
  lookup: [90mundefined[39m,
  rejectUnauthorized: [33mtrue[39m,
  noDelay: [33mtrue[39m,
  servername: [32m''[39m,
  _agentKey: [32m'127.0.0.1:38201:'[39m
}
HTTP [33m3985633[39m: sockets 127.0.0.1:38201: [33m1[39m [33m1[39m

[2025-06-23T13:28:02.753Z]   [Act Log] - +23ms [process.stderr] HTTP [33m3985633[39m: write ret = true
HTTP [33m3985633[39m: outgoing message end.

[2025-06-23T13:28:02.753Z]   [Act Log] - +23ms [process.stderr] HTTP [33m3985633[39m: SERVER new http connection

[2025-06-23T13:28:02.753Z]   [Act Log] - +24ms [process.stderr] HTTP [33m3985633[39m: SERVER socketOnParserExecute 274

[2025-06-23T13:28:02.753Z]   [Act Log] - +27ms [process.stderr] HTTP [33m3985633[39m: write ret = true

[2025-06-23T13:28:02.753Z]   [Act Log] - +27ms [process.stderr] HTTP [33m3985633[39m: outgoing message end.

[2025-06-23T13:28:02.753Z]   [Act Log] - +27ms [process.stderr] HTTP [33m3985633[39m: server socket close

[2025-06-23T13:28:02.753Z]   [Act Log] - +27ms [process.stderr] HTTP [33m3985633[39m: AGENT incoming response!

[2025-06-23T13:28:02.753Z]   [Act Log] - +27ms [process.stderr] HTTP [33m3985633[39m: AGENT socket.destroySoon()

[2025-06-23T13:28:02.753Z]   [Act Log] - === End Program Output ===
[2025-06-23T13:28:02.753Z]   [Act Log] - Method execution completed successfully
[2025-06-23T13:28:02.753Z]   [Act Log] - No exceptions thrown during execution
[2025-06-23T13:28:02.753Z]   [Act Log] - All function calls returned expected types
[2025-06-23T13:28:02.753Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-23T13:28:02.753Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-23T13:28:02.753Z]   [RESULT] - PASS: Test completed successfully
[2025-06-23T13:28:02.753Z]   [CASE END] - Duration: 32ms
[2025-06-23T13:28:02.845Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-23T13:28:02.845Z] Suite Duration: 20390ms
[2025-06-23T13:28:02.845Z] Suite Results: 6 passed, 0 failed, 0 skipped
[2025-06-23T13:28:03.484Z] 
================================================================================
[2025-06-23T13:28:03.484Z] Test Run Finished
[2025-06-23T13:28:03.484Z] End Timestamp: 2025-06-23T13:28:03.484Z
[2025-06-23T13:28:03.484Z] Total Duration: 21506ms (21.51s)
[2025-06-23T13:28:03.484Z] 
[STATISTICS]
[2025-06-23T13:28:03.484Z] Total Test Suites: 14
[2025-06-23T13:28:03.484Z] Passed Test Suites: 12
[2025-06-23T13:28:03.484Z] Failed Test Suites: 2
[2025-06-23T13:28:03.484Z] Total Tests: 52
[2025-06-23T13:28:03.484Z] Passed Tests: 45
[2025-06-23T13:28:03.484Z] Failed Tests: 7
[2025-06-23T13:28:03.485Z] Skipped Tests: 0
[2025-06-23T13:28:03.485Z] Success Rate: 86.54%
[2025-06-23T13:28:03.485Z] Overall Result: FAILURE
[2025-06-23T13:28:03.485Z] ================================================================================
